services:
  # HDFS NameNode
  namenode:
    image: bde2020/hadoop-namenode:2.0.0-hadoop3.2.1-java8
    container_name: namenode
    restart: always
    ports:
      - "9870:9870"  # Web UI
      - "8020:8020"  # HDFS API
    volumes:
      - namenode_data:/hadoop/dfs/name
      - ./data:/data  # Mount host data directory
    environment:
      - CLUSTER_NAME=bigdata_cluster
      - CORE_CONF_fs_defaultFS=hdfs://namenode:8020
      - CORE_CONF_hadoop_http_staticuser_user=root
      - CORE_CONF_hadoop_proxyuser_hue_hosts=*
      - CORE_CONF_hadoop_proxyuser_hue_groups=*
      - CORE_CONF_io_compression_codecs=org.apache.hadoop.io.compress.SnappyCodec
      - HDFS_CONF_dfs_webhdfs_enabled=true
      - HDFS_CONF_dfs_permissions_enabled=false
      - HDFS_CONF_dfs_nameservices=bigdata_cluster
    networks:
      - bigdata_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9870"]
      interval: 30s
      timeout: 10s
      retries: 3

  # HDFS DataNode
  datanode:
    image: bde2020/hadoop-datanode:2.0.0-hadoop3.2.1-java8
    container_name: datanode
    restart: always
    ports:
      - "9864:9864"  # Web UI
    volumes:
      - datanode_data:/hadoop/dfs/data
    environment:
      - CORE_CONF_fs_defaultFS=hdfs://namenode:8020
      - CORE_CONF_hadoop_http_staticuser_user=root
      - CORE_CONF_hadoop_proxyuser_hue_hosts=*
      - CORE_CONF_hadoop_proxyuser_hue_groups=*
      - CORE_CONF_io_compression_codecs=org.apache.hadoop.io.compress.SnappyCodec
      - HDFS_CONF_dfs_webhdfs_enabled=true
      - HDFS_CONF_dfs_permissions_enabled=false
    depends_on:
      namenode:
        condition: service_healthy
    networks:
      - bigdata_network

  # Apache Spark Master
  spark-master:
    image: bitnami/spark:3.4
    container_name: spark-master
    restart: always
    ports:
      - "8080:8080"  # Web UI
      - "7077:7077"  # Spark Master
    environment:
      - SPARK_MODE=master
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - ./spark-scripts:/opt/spark-scripts
      - ./spark-jars:/opt/spark/jars/custom
    networks:
      - bigdata_network

  # Apache Spark Worker
  spark-worker:
    image: bitnami/spark:3.4
    container_name: spark-worker
    restart: always
    ports:
      - "8081:8081"  # Web UI
    environment:
      - SPARK_MODE=worker
      - SPARK_MASTER_URL=spark://spark-master:7077
      - SPARK_WORKER_MEMORY=2G
      - SPARK_WORKER_CORES=2
      - SPARK_RPC_AUTHENTICATION_ENABLED=no
      - SPARK_RPC_ENCRYPTION_ENABLED=no
      - SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED=no
      - SPARK_SSL_ENABLED=no
    volumes:
      - ./spark-scripts:/opt/spark-scripts
      - ./spark-jars:/opt/spark/jars/custom
    depends_on:
      - spark-master
    networks:
      - bigdata_network

  # PostgreSQL for Hive Metastore
  postgres-hive:
    image: postgres:13
    container_name: postgres-hive
    restart: always
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=metastore_db
      - POSTGRES_USER=hive
      - POSTGRES_PASSWORD=hive123
    volumes:
      - postgres_hive_data:/var/lib/postgresql/data
    networks:
      - bigdata_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hive -d metastore_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Hive Metastore
  hive-metastore:
    image: apache/hive:3.1.3
    container_name: hive-metastore
    restart: always
    ports:
      - "9083:9083"  # Thrift server
    environment:
      - SERVICE_NAME=metastore
      - DB_DRIVER=postgres
      - SERVICE_OPTS=-Djavax.jdo.option.ConnectionDriverName=org.postgresql.Driver -Djavax.jdo.option.ConnectionURL=************************************************* -Djavax.jdo.option.ConnectionUserName=hive -Djavax.jdo.option.ConnectionPassword=hive123
      - HIVE_CUSTOM_CONF_DIR=/hive_custom_conf
    volumes:
      - ./hive-conf:/hive_custom_conf
      - hive_warehouse:/opt/hive/data/warehouse
    depends_on:
      postgres-hive:
        condition: service_healthy
      namenode:
        condition: service_healthy
    networks:
      - bigdata_network

  # HiveServer2
  hiveserver2:
    image: apache/hive:3.1.3
    container_name: hiveserver2
    restart: always
    ports:
      - "10000:10000"  # JDBC/ODBC
      - "10002:10002"  # Web UI
    environment:
      - SERVICE_NAME=hiveserver2
      - SERVICE_OPTS=-Dhive.metastore.uris=thrift://hive-metastore:9083
      - IS_RESUME=true
      - HIVE_CUSTOM_CONF_DIR=/hive_custom_conf
    volumes:
      - ./hive-conf:/hive_custom_conf
      - hive_warehouse:/opt/hive/data/warehouse
    depends_on:
      - hive-metastore
    networks:
      - bigdata_network

  # Apache NiFi
  nifi:
    image: apache/nifi:1.23.2
    container_name: nifi
    restart: always
    ports:
      - "8443:8443"  # Web UI (HTTPS)
      - "8090:8080"  # Web UI (HTTP, if configured)
    environment:
      - NIFI_WEB_HTTPS_PORT=8443
      - NIFI_WEB_HTTPS_HOST=0.0.0.0
      - NIFI_WEB_PROXY_HOST=localhost:8443
      - SINGLE_USER_CREDENTIALS_USERNAME=admin
      - SINGLE_USER_CREDENTIALS_PASSWORD=password123456789
      - NIFI_SENSITIVE_PROPS_KEY=nifi_secret_key_12345678901234567890
    volumes:
      - nifi_database_repository:/opt/nifi/nifi-current/database_repository
      - nifi_flowfile_repository:/opt/nifi/nifi-current/flowfile_repository
      - nifi_content_repository:/opt/nifi/nifi-current/content_repository
      - nifi_provenance_repository:/opt/nifi/nifi-current/provenance_repository
      - nifi_state:/opt/nifi/nifi-current/state
      - nifi_logs:/opt/nifi/nifi-current/logs
      - ./nifi-templates:/opt/nifi/nifi-current/conf/templates
      - ./data:/data  # Mount host data directory
    depends_on:
      namenode:
        condition: service_healthy
    networks:
      - bigdata_network
    healthcheck:
      test: ["CMD", "curl", "-k", "-f", "https://localhost:8443/nifi"]
      interval: 60s
      timeout: 30s
      retries: 3

  # PostgreSQL for Airflow
  postgres-airflow:
    image: postgres:13
    container_name: postgres-airflow
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=airflow
      - POSTGRES_USER=airflow
      - POSTGRES_PASSWORD=airflow123
    volumes:
      - postgres_airflow_data:/var/lib/postgresql/data
    networks:
      - bigdata_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U airflow -d airflow"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Apache Airflow Init (one-time setup)
  airflow-init:
    image: apache/airflow:2.7.1-python3.9
    container_name: airflow-init
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow123@postgres-airflow:5432/airflow
      - AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=true
      - AIRFLOW__CORE__LOAD_EXAMPLES=false
      - _AIRFLOW_DB_UPGRADE=true
      - _AIRFLOW_WWW_USER_CREATE=true
      - _AIRFLOW_WWW_USER_USERNAME=admin
      - _AIRFLOW_WWW_USER_PASSWORD=admin123
    volumes:
      - ./airflow-dags:/opt/airflow/dags
      - ./airflow-logs:/opt/airflow/logs
      - ./airflow-plugins:/opt/airflow/plugins
      - ./spark-scripts:/opt/spark-scripts
    user: "0:0"  # Run as root to fix permissions
    depends_on:
      postgres-airflow:
        condition: service_healthy
    networks:
      - bigdata_network
    command: >
      bash -c "
        # Fix permissions
        chown -R 50000:50000 /opt/airflow/dags /opt/airflow/logs /opt/airflow/plugins
        # Initialize database
        airflow db init
        # Create admin user
        airflow users create --username admin --firstname Admin --lastname User --role Admin --email <EMAIL> --password admin123 || true
      "

  # Apache Airflow Webserver
  airflow-webserver:
    image: apache/airflow:2.7.1-python3.9
    container_name: airflow-webserver
    restart: always
    ports:
      - "8082:8080"  # Web UI
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow123@postgres-airflow:5432/airflow
      - AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=true
      - AIRFLOW__CORE__LOAD_EXAMPLES=false
      - AIRFLOW__WEBSERVER__EXPOSE_CONFIG=true
      - AIRFLOW__WEBSERVER__WEB_SERVER_PORT=8080
      - AIRFLOW__WEBSERVER__WEB_SERVER_HOST=0.0.0.0
      - AIRFLOW__CORE__FERNET_KEY=YlCImzjge_TeZc7jPJ7Jz2pgOtb4yTssA1pVyqIADWg=
    volumes:
      - ./airflow-dags:/opt/airflow/dags
      - ./airflow-logs:/opt/airflow/logs
      - ./airflow-plugins:/opt/airflow/plugins
      - ./spark-scripts:/opt/spark-scripts
    user: "50000:50000"  # Use airflow user
    depends_on:
      postgres-airflow:
        condition: service_healthy
    networks:
      - bigdata_network
    command: >
      bash -c "
        # Wait for database to be ready
        sleep 15
        # Initialize database if needed
        airflow db check || airflow db init
        # Start webserver with explicit host binding
        airflow webserver --port 8080 --hostname 0.0.0.0
      "

  # Apache Airflow Scheduler
  airflow-scheduler:
    image: apache/airflow:2.7.1-python3.9
    container_name: airflow-scheduler
    restart: always
    environment:
      - AIRFLOW__CORE__EXECUTOR=LocalExecutor
      - AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow123@postgres-airflow:5432/airflow
      - AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=true
      - AIRFLOW__CORE__LOAD_EXAMPLES=false
    volumes:
      - ./airflow-dags:/opt/airflow/dags
      - ./airflow-logs:/opt/airflow/logs
      - ./airflow-plugins:/opt/airflow/plugins
      - ./spark-scripts:/opt/spark-scripts
    user: "50000:50000"  # Use airflow user
    depends_on:
      postgres-airflow:
        condition: service_healthy
    networks:
      - bigdata_network
    command: >
      bash -c "
        # Wait for database and webserver to be ready
        sleep 15
        # Start scheduler
        airflow scheduler
      "

volumes:
  namenode_data:
  datanode_data:
  postgres_hive_data:
  postgres_airflow_data:
  hive_warehouse:
  nifi_database_repository:
  nifi_flowfile_repository:
  nifi_content_repository:
  nifi_provenance_repository:
  nifi_state:
  nifi_logs:

networks:
  bigdata_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16