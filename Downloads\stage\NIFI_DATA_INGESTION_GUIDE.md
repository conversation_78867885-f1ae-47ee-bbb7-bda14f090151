# Apache NiFi Data Ingestion Guide - Bank Dataset from HDFS

## 🎯 Overview

This guide walks you through creating a complete data ingestion pipeline using Apache NiFi to process the bank.csv dataset stored in HDFS.

## 🔐 Access NiFi

### Step 1: Open NiFi Web UI

- **URL**: https://localhost:8443
- **Username**: `admin`
- **Password**: `password123456`

**Note**: You may see a security warning due to self-signed certificate - click "Advanced" and "Proceed to localhost"

## 🏗️ Manual Pipeline Creation

### Step 2: Create Process Group

1. **Drag** a "Process Group" from the toolbar to the canvas
2. **Name**: "Bank Data Ingestion Pipeline"
3. **Double-click** to enter the process group

### Step 3: Add ListHDFS Processor

1. **Drag** a "Processor" to the canvas
2. **Search**: "ListHDFS"
3. **Select**: "ListHDFS" processor
4. **Configure**:
   - **Name**: "List Bank Data Files"
   - **Properties**:
     - **Directory**: `/user/datasets`
     - **Recurse Subdirectories**: `false`
   - **Scheduling**:
     - **Run Schedule**: `1 min`

### Step 4: Add FetchHDFS Processor

1. **Drag** another "Processor" to the canvas
2. **Search**: "FetchHDFS"
3. **Select**: "FetchHDFS" processor
4. **Configure**:
   - **Name**: "Fetch Bank Data"
   - **Properties**:
     - **HDFS Filename**: `${path}/${filename}`
   - **Settings**:
     - **Auto terminate relationships**: Check "failure" and "comms.failure"

### Step 5: Connect Processors

1. **Hover** over ListHDFS processor
2. **Drag** the arrow to FetchHDFS processor
3. **Select** "success" relationship

### Step 6: Add ConvertRecord Processor (Optional)

1. **Drag** a "Processor" to the canvas
2. **Search**: "ConvertRecord"
3. **Configure**:
   - **Name**: "CSV to JSON Converter"
   - **Record Reader**: Create CSV Reader Controller Service
   - **Record Writer**: Create JSON Writer Controller Service

## 🔧 Controller Services Configuration

### CSV Reader Service

1. **Go to**: Controller Services (gear icon)
2. **Add Service**: "CSVReader"
3. **Configure**:
   - **Name**: "CSV Reader - Bank Data"
   - **Schema Access Strategy**: "Use String Fields From Header"
   - **Treat First Line as Header**: `true`
   - **CSV Format**: "Custom Format"

### JSON Writer Service

1. **Add Service**: "JsonRecordSetWriter"
2. **Configure**:
   - **Name**: "JSON Writer - Bank Data"
   - **Schema Write Strategy**: "Do Not Write Schema"

## 🚀 Pipeline Execution

### Step 7: Start the Pipeline

1. **Select all processors** (Ctrl+A)
2. **Right-click** → "Start"
3. **Monitor** the flow in real-time

### Expected Flow:

```
ListHDFS → FetchHDFS → ConvertRecord → [Output]
```

## 📊 Data Processing Options

### Option 1: Simple File Transfer

**ListHDFS** → **FetchHDFS** → **PutFile** (to local directory)

### Option 2: Format Conversion

**ListHDFS** → **FetchHDFS** → **ConvertRecord** → **PutFile** (CSV to JSON)

### Option 3: Data Validation

**ListHDFS** → **FetchHDFS** → **ValidateRecord** → **RouteOnAttribute** → **PutFile**

### Option 4: Database Loading

**ListHDFS** → **FetchHDFS** → **ConvertRecord** → **PutDatabaseRecord**

## 🔍 Monitoring and Troubleshooting

### Check Data Flow

1. **Right-click** on connections → "List queue"
2. **View FlowFile** content and attributes
3. **Monitor** processor statistics

### Common Issues and Solutions

#### Issue 1: HDFS Connection Failed

**Solution**:

- Verify HDFS is running: `docker ps | grep namenode`
- Check HDFS connectivity: `docker exec namenode hdfs dfs -ls /user/datasets`

#### Issue 2: File Not Found

**Solution**:

- Verify file exists: `docker exec namenode hdfs dfs -ls /user/datasets/bank.csv`
- Check directory path in ListHDFS processor

#### Issue 3: Permission Denied

**Solution**:

- Check HDFS permissions: `docker exec namenode hdfs dfs -ls -la /user/datasets`
- Modify permissions if needed: `docker exec namenode hdfs dfs -chmod 755 /user/datasets`

## 📈 Advanced Processing Examples

### Example 1: Data Quality Checks

```
FetchHDFS → SplitRecord → ValidateRecord → RouteOnAttribute
                                        ↓
                                   [Valid] → PutFile
                                   [Invalid] → LogAttribute
```

### Example 2: Real-time Analytics

```
FetchHDFS → ConvertRecord → EvaluateJsonPath → UpdateAttribute → PutKafka
```

### Example 3: Data Enrichment

```
FetchHDFS → ConvertRecord → LookupRecord → MergeRecord → PutHDFS
```

## 🎯 Expected Results

### Successful Pipeline Output:

- **Files Processed**: 1 (bank.csv)
- **Records**: ~45,000 bank records
- **Format**: CSV → JSON (if using ConvertRecord)
- **Destination**: Configurable (local file, database, Kafka, etc.)

### FlowFile Attributes:

- **filename**: bank.csv
- **path**: /user/datasets
- **hdfs.file.size**: 921KB
- **record.count**: ~45,000

## 🔧 Useful NiFi Commands

### Via NiFi UI:

- **Start All**: Select all → Right-click → Start
- **Stop All**: Select all → Right-click → Stop
- **View Data**: Right-click connection → List queue → View FlowFile

### Via Container:

```bash
# Check NiFi logs
docker logs nifi

# Access NiFi container
docker exec -it nifi bash

# Check NiFi status
docker exec nifi ps aux | grep nifi
```

## 📋 Verification Checklist

- [ ] NiFi UI accessible at https://localhost:8443
- [ ] ListHDFS processor configured with correct directory
- [ ] FetchHDFS processor connected and configured
- [ ] Controller services enabled (if using ConvertRecord)
- [ ] Pipeline started and running
- [ ] Data flowing through connections
- [ ] Output destination configured
- [ ] No error messages in processor logs

## 🎉 Success Indicators

1. **Green status** on all processors
2. **Data flowing** through connections (numbers increasing)
3. **FlowFiles** visible in queues
4. **Output files** created in destination
5. **No error bulletins** in NiFi UI

Your bank dataset is now being processed through Apache NiFi's powerful data ingestion pipeline!
