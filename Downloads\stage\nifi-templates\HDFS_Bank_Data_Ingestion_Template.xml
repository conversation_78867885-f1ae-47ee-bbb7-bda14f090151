<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<template encoding-version="1.3">
    <description>Template for ingesting bank.csv data from HDFS and processing it</description>
    <groupId>root</groupId>
    <name>HDFS Bank Data Ingestion</name>
    <snippet>
        <processGroups>
            <id>bank-data-ingestion</id>
            <parentGroupId>root</parentGroupId>
            <position>
                <x>0.0</x>
                <y>0.0</y>
            </position>
            <comments>Bank data ingestion from HDFS with processing pipeline</comments>
            <contents>
                <processors>
                    <!-- HDFS List Processor -->
                    <id>hdfs-list-processor</id>
                    <parentGroupId>bank-data-ingestion</parentGroupId>
                    <position>
                        <x>100.0</x>
                        <y>100.0</y>
                    </position>
                    <bundle>
                        <artifact>nifi-hadoop-nar</artifact>
                        <group>org.apache.nifi</group>
                        <version>1.23.2</version>
                    </bundle>
                    <config>
                        <bulletinLevel>WARN</bulletinLevel>
                        <comments>Lists files in HDFS directory</comments>
                        <concurrentlySchedulableTaskCount>1</concurrentlySchedulableTaskCount>
                        <descriptors>
                            <entry>
                                <key>Hadoop Configuration Resources</key>
                                <value>
                                    <name>Hadoop Configuration Resources</name>
                                </value>
                            </entry>
                            <entry>
                                <key>Directory</key>
                                <value>
                                    <name>Directory</name>
                                </value>
                            </entry>
                            <entry>
                                <key>Recurse Subdirectories</key>
                                <value>
                                    <name>Recurse Subdirectories</name>
                                </value>
                            </entry>
                        </descriptors>
                        <executionNode>ALL</executionNode>
                        <lossTolerant>false</lossTolerant>
                        <penaltyDuration>30 sec</penaltyDuration>
                        <properties>
                            <entry>
                                <key>Directory</key>
                                <value>/user/datasets</value>
                            </entry>
                            <entry>
                                <key>Recurse Subdirectories</key>
                                <value>false</value>
                            </entry>
                        </properties>
                        <runDurationMillis>0</runDurationMillis>
                        <schedulingPeriod>1 min</schedulingPeriod>
                        <schedulingStrategy>TIMER_DRIVEN</schedulingStrategy>
                        <yieldDuration>1 sec</yieldDuration>
                    </config>
                    <name>List HDFS Bank Data</name>
                    <relationships>
                        <autoTerminate>false</autoTerminate>
                        <name>success</name>
                    </relationships>
                    <state>STOPPED</state>
                    <style/>
                    <type>org.apache.nifi.processors.hadoop.ListHDFS</type>
                </processors>
                
                <!-- HDFS Fetch Processor -->
                <processors>
                    <id>hdfs-fetch-processor</id>
                    <parentGroupId>bank-data-ingestion</parentGroupId>
                    <position>
                        <x>400.0</x>
                        <y>100.0</y>
                    </position>
                    <bundle>
                        <artifact>nifi-hadoop-nar</artifact>
                        <group>org.apache.nifi</group>
                        <version>1.23.2</version>
                    </bundle>
                    <config>
                        <bulletinLevel>WARN</bulletinLevel>
                        <comments>Fetches files from HDFS</comments>
                        <concurrentlySchedulableTaskCount>1</concurrentlySchedulableTaskCount>
                        <descriptors>
                            <entry>
                                <key>HDFS Filename</key>
                                <value>
                                    <name>HDFS Filename</name>
                                </value>
                            </entry>
                        </descriptors>
                        <executionNode>ALL</executionNode>
                        <lossTolerant>false</lossTolerant>
                        <penaltyDuration>30 sec</penaltyDuration>
                        <properties>
                            <entry>
                                <key>HDFS Filename</key>
                                <value>${path}/${filename}</value>
                            </entry>
                        </properties>
                        <runDurationMillis>0</runDurationMillis>
                        <schedulingPeriod>0 sec</schedulingPeriod>
                        <schedulingStrategy>TIMER_DRIVEN</schedulingStrategy>
                        <yieldDuration>1 sec</yieldDuration>
                    </config>
                    <name>Fetch HDFS Bank Data</name>
                    <relationships>
                        <autoTerminate>false</autoTerminate>
                        <name>success</name>
                    </relationships>
                    <relationships>
                        <autoTerminate>true</autoTerminate>
                        <name>failure</name>
                    </relationships>
                    <relationships>
                        <autoTerminate>true</autoTerminate>
                        <name>comms.failure</name>
                    </relationships>
                    <state>STOPPED</state>
                    <style/>
                    <type>org.apache.nifi.processors.hadoop.FetchHDFS</type>
                </processors>

                <!-- CSV Record Reader Controller Service -->
                <controllerServices>
                    <id>csv-reader-service</id>
                    <parentGroupId>bank-data-ingestion</parentGroupId>
                    <bundle>
                        <artifact>nifi-record-serialization-services-nar</artifact>
                        <group>org.apache.nifi</group>
                        <version>1.23.2</version>
                    </bundle>
                    <comments>CSV Record Reader for bank data</comments>
                    <descriptors>
                        <entry>
                            <key>schema-access-strategy</key>
                            <value>
                                <name>schema-access-strategy</name>
                            </value>
                        </entry>
                        <entry>
                            <key>csv-format</key>
                            <value>
                                <name>csv-format</name>
                            </value>
                        </entry>
                        <entry>
                            <key>csv-header-line-skip-count</key>
                            <value>
                                <name>csv-header-line-skip-count</name>
                            </value>
                        </entry>
                    </descriptors>
                    <name>CSV Reader - Bank Data</name>
                    <properties>
                        <entry>
                            <key>schema-access-strategy</key>
                            <value>csv-header-derived</value>
                        </entry>
                        <entry>
                            <key>csv-format</key>
                            <value>custom</value>
                        </entry>
                        <entry>
                            <key>csv-header-line-skip-count</key>
                            <value>0</value>
                        </entry>
                    </properties>
                    <state>ENABLED</state>
                    <type>org.apache.nifi.csv.CSVReader</type>
                </controllerServices>

                <!-- JSON Record Writer Controller Service -->
                <controllerServices>
                    <id>json-writer-service</id>
                    <parentGroupId>bank-data-ingestion</parentGroupId>
                    <bundle>
                        <artifact>nifi-record-serialization-services-nar</artifact>
                        <group>org.apache.nifi</group>
                        <version>1.23.2</version>
                    </bundle>
                    <comments>JSON Record Writer for processed data</comments>
                    <name>JSON Writer - Bank Data</name>
                    <properties>
                        <entry>
                            <key>schema-write-strategy</key>
                            <value>no-schema</value>
                        </entry>
                        <entry>
                            <key>suppress-nulls</key>
                            <value>never-suppress</value>
                        </entry>
                    </properties>
                    <state>ENABLED</state>
                    <type>org.apache.nifi.json.JsonRecordSetWriter</type>
                </controllerServices>

                <!-- Connections -->
                <connections>
                    <id>list-to-fetch-connection</id>
                    <parentGroupId>bank-data-ingestion</parentGroupId>
                    <backPressureDataSizeThreshold>1 GB</backPressureDataSizeThreshold>
                    <backPressureObjectThreshold>10000</backPressureObjectThreshold>
                    <destination>
                        <groupId>bank-data-ingestion</groupId>
                        <id>hdfs-fetch-processor</id>
                        <type>PROCESSOR</type>
                    </destination>
                    <flowFileExpiration>0 sec</flowFileExpiration>
                    <labelIndex>1</labelIndex>
                    <name></name>
                    <selectedRelationships>success</selectedRelationships>
                    <source>
                        <groupId>bank-data-ingestion</groupId>
                        <id>hdfs-list-processor</id>
                        <type>PROCESSOR</type>
                    </source>
                    <zIndex>0</zIndex>
                </connections>
            </contents>
            <name>Bank Data Ingestion Pipeline</name>
        </processGroups>
    </snippet>
</template>
