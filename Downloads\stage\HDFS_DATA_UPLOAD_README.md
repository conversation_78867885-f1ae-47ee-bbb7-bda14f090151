# HDFS Data Upload Pipeline - Complete Guide

## 📋 Overview

This document explains the complete process of uploading data to HDFS (Hadoop Distributed File System), including the architecture, commands used, and the data flow from local storage to distributed datanodes.

## 🏗️ HDFS Architecture Components

### NameNode
- **Role**: Master node that manages metadata and file system namespace
- **Container**: `namenode`
- **Port**: 9870 (Web UI), 8020 (HDFS API)
- **Function**: 
  - Stores file metadata (file names, permissions, block locations)
  - Manages file system operations
  - Coordinates data placement across datanodes

### DataNode
- **Role**: Worker nodes that store actual data blocks
- **Container**: `datanode`
- **Port**: 9864 (Web UI)
- **Function**:
  - Stores data blocks (default 128MB per block)
  - Handles read/write requests from clients
  - Reports block status to NameNode
  - Performs block replication

## 📊 Data Upload Pipeline Process

### Step 1: Environment Verification
**Purpose**: Verify HDFS cluster is running and accessible

**Command Used**:
```bash
docker exec namenode hdfs dfs -ls /
```

**Output**:
```
Found 2 items
drwxr-xr-x   - root supergroup          0 2025-08-07 23:55 /data
drwxr-xr-x   - root supergroup          0 2025-08-07 23:09 /user
```

**What Happens**:
- Client connects to NameNode via HDFS API
- NameNode returns root directory listing
- Confirms HDFS cluster is operational

### Step 2: Directory Structure Creation
**Purpose**: Create organized directory structure for data storage

**Command Used**:
```bash
docker exec namenode hdfs dfs -mkdir -p /user/datasets
```

**What Happens**:
1. Client sends mkdir request to NameNode
2. NameNode creates directory metadata
3. Directory structure is established in namespace
4. No data blocks created (directories are metadata only)

### Step 3: Local File Transfer to Container
**Purpose**: Move local file into Docker container for HDFS access

**Command Used**:
```bash
docker cp bank.csv namenode:/tmp/bank.csv
```

**Output**:
```
Successfully copied 921kB to namenode:/tmp/bank.csv
```

**What Happens**:
- Docker copies file from host filesystem to container
- File temporarily stored in container's `/tmp` directory
- File size: 921KB (original size)

### Step 4: HDFS Upload (Put Operation)
**Purpose**: Upload file from container to HDFS distributed storage

**Command Used**:
```bash
docker exec namenode hdfs dfs -put /tmp/bank.csv /user/datasets/
```

**Detailed Process**:

#### 4.1 Client Request
- HDFS client (in namenode container) initiates put operation
- Client contacts NameNode to request file upload

#### 4.2 NameNode Processing
- NameNode allocates blocks for the file
- Determines DataNode locations for block placement
- Creates file metadata entry
- Returns DataNode pipeline information to client

#### 4.3 Data Pipeline Creation
**Output Log**:
```
2025-08-08 16:03:15,458 INFO sasl.SaslDataTransferClient: SASL encryption trust check: localHostTrusted = false, remoteHostTrusted = false
2025-08-08 16:03:45,753 WARN hdfs.DataStreamer: Slow waitForAckedSeqno took 46974ms (threshold=30000ms). File being written: /user/datasets/bank.csv._COPYING_, block: BP-*********-**********-*************:blk_1073741825_1001, Write pipeline datanodes: [DatanodeInfoWithStorage[***********:9866,DS-13e393dc-b278-46dd-8b00-51c2857cb16d,DISK]].
```

**Pipeline Details**:
- **Block ID**: `blk_1073741825_1001`
- **DataNode**: `***********:9866`
- **Storage Type**: DISK
- **Temporary filename**: `bank.csv._COPYING_` (during upload)

#### 4.4 Data Streaming
1. Client streams data to first DataNode in pipeline
2. DataNode receives data and writes to local storage
3. DataNode forwards data to next DataNode (if replication > 1)
4. Each DataNode acknowledges successful write
5. Process continues until all replicas are written

#### 4.5 Finalization
- All DataNodes confirm successful write
- NameNode updates metadata with final block locations
- Temporary file renamed to final name
- File becomes available for read operations

## ✅ Verification Commands

### Check File Existence
```bash
docker exec namenode hdfs dfs -ls /user/datasets/
```

### Check File Size and Replication
```bash
docker exec namenode hdfs dfs -du -h /user/datasets/
```

**Output**:
```
897.4 K  2.6 M  /user/datasets/bank.csv
```

**Explanation**:
- **897.4 K**: Actual file size
- **2.6 M**: Total storage used (including replication)
- **Replication Factor**: ~3x (2.6M ÷ 897.4K ≈ 3)

### View File Content
```bash
docker exec namenode hdfs dfs -cat /user/datasets/bank.csv | head -10
```

### Get File Statistics
```bash
docker exec namenode hdfs dfs -stat /user/datasets/bank.csv
```

## 🔧 Technical Details

### Block Storage
- **Default Block Size**: 128MB (file smaller than one block)
- **Replication Factor**: 3 (configurable)
- **Storage Policy**: Default (all replicas on disk)

### Network Communication
- **NameNode API**: Port 8020
- **DataNode Data Transfer**: Port 9866
- **Web UI**: NameNode (9870), DataNode (9864)

### Data Integrity
- **Checksums**: Automatically calculated and verified
- **Block Reports**: DataNodes regularly report block status
- **Heartbeats**: DataNodes send heartbeats to NameNode

## 📁 File Locations

### Host System
- **Original File**: `C:\Users\<USER>\Downloads\stage\bank.csv`

### Container Temporary
- **Temporary Location**: `/tmp/bank.csv` (in namenode container)

### HDFS Distributed Storage
- **Final Location**: `/user/datasets/bank.csv`
- **Physical Storage**: Distributed across DataNode containers
- **Metadata**: Stored in NameNode

## 🌐 Access Methods

### Command Line
```bash
# List files
docker exec namenode hdfs dfs -ls /user/datasets/

# Read file
docker exec namenode hdfs dfs -cat /user/datasets/bank.csv

# Copy from HDFS
docker exec namenode hdfs dfs -get /user/datasets/bank.csv /tmp/downloaded.csv
```

### Web UI
- **URL**: http://localhost:9870
- **Path**: Utilities → Browse the file system → /user/datasets/

### Programmatic Access
- **Spark**: `spark.read.csv("hdfs://namenode:8020/user/datasets/bank.csv")`
- **Hive**: Create external table pointing to HDFS location
- **Java/Python**: Use HDFS client libraries

## 🎯 Summary

The HDFS upload pipeline successfully:
1. ✅ Verified cluster connectivity
2. ✅ Created directory structure
3. ✅ Transferred file to container
4. ✅ Uploaded to distributed storage
5. ✅ Replicated across datanodes
6. ✅ Made available for big data processing

**Result**: 921KB dataset now distributed and replicated across HDFS cluster, ready for analysis with Spark, Hive, and other big data tools.
